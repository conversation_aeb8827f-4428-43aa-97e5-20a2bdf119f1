<script>
  import { fly } from 'svelte/transition';
  import { Button } from '$lib/components/ui/button';
  import { Badge } from '$lib/components/ui/badge';
  import { Clock, Target, Zap, X, Search, FileText, Send, ArrowLeftRight } from 'lucide-svelte';

  let visible = $state(false);
  let automationStep = $state(0);
  let sliderPosition = $state(0); // 0 = show chaos, 100 = show automation
  let isDragging = $state(false);
  let stickyNoteAnimations = $state([]);

  $effect(() => {
    visible = true;

    // Automation step animations
    const automationTimer = setInterval(() => {
      automationStep = (automationStep + 1) % 4;
    }, 2500);

    // Auto-slide demo every 6 seconds
    const slideTimer = setInterval(() => {
      if (!isDragging) {
        sliderPosition = sliderPosition === 0 ? 100 : 0;
      }
    }, 6000);

    // Random sticky note animations
    const stickyTimer = setInterval(() => {
      const randomIndex = Math.floor(Math.random() * 12);
      stickyNoteAnimations = [...stickyNoteAnimations, randomIndex].slice(-2);
    }, 2000);

    return () => {
      clearInterval(automationTimer);
      clearInterval(slideTimer);
      clearInterval(stickyTimer);
    };
  });

  // Handle slider drag
  function handleSliderDrag(event) {
    if (!isDragging) return;

    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    sliderPosition = percentage;
  }

  function startDrag() {
    isDragging = true;
  }

  function stopDrag() {
    isDragging = false;
  }
</script>

<section
  class="relative min-h-screen overflow-hidden bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
  <div class="relative z-10 flex min-h-screen flex-col">
    <!-- Main heading -->
    {#if visible}
      <div class="pb-12 pt-20 text-center">
        <div
          in:fly={{ y: 30, duration: 1000 }}
          class="mb-8 text-5xl font-bold tracking-tight md:text-6xl lg:text-7xl">
          Stop <span class="bg-gradient-to-r from-red-500 to-red-600 bg-clip-text text-transparent"
            >struggling</span>
          with job hunting.
          <br />
          Start
          <span
            class="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent"
            >automating</span> your success.
        </div>
        <p
          in:fly={{ y: 30, duration: 1000, delay: 200 }}
          class="mx-auto max-w-4xl text-xl font-light leading-relaxed text-slate-600 md:text-2xl dark:text-slate-300">
          Drag the slider to see the transformation from chaos to automation
        </p>
      </div>
    {/if}

    <!-- Desktop Monitor with Slider -->
    <div class="flex-1 px-4 lg:px-8">
      {#if visible}
        <div in:fly={{ y: 50, duration: 1000, delay: 400 }} class="relative mx-auto max-w-6xl">
          <!-- Monitor Stand -->
          <div class="mb-6 flex flex-col items-center">
            <!-- Monitor Base -->
            <div
              class="h-4 w-56 rounded-full bg-gradient-to-b from-slate-600 to-slate-700 shadow-2xl">
            </div>
            <!-- Monitor Neck -->
            <div
              class="-mt-2 h-12 w-8 rounded-t-lg bg-gradient-to-b from-slate-500 to-slate-600 shadow-lg">
            </div>
          </div>

          <!-- Monitor Frame -->
          <div
            class="relative rounded-3xl border border-slate-700 bg-gradient-to-b from-slate-800 to-slate-900 p-6 shadow-2xl"
            style="aspect-ratio: 16/10;">
            <!-- Monitor Bezel -->
            <div
              class="absolute inset-4 rounded-2xl bg-gradient-to-b from-slate-700 to-slate-800 shadow-inner">
            </div>

            <!-- Screen -->
            <div
              class="relative h-full w-full cursor-grab overflow-hidden rounded-2xl bg-black shadow-2xl active:cursor-grabbing"
              on:mousemove={handleSliderDrag}
              on:mouseup={stopDrag}
              on:mouseleave={stopDrag}
              role="slider"
              tabindex="0"
              aria-valuemin="0"
              aria-valuemax="100"
              aria-valuenow={sliderPosition}
              aria-label="Drag to compare manual vs automated job hunting">
              <!-- Screen Reflection -->
              <div
                class="pointer-events-none absolute inset-0 z-50 rounded-2xl bg-gradient-to-br from-white/5 via-transparent to-transparent">
              </div>
              <!-- Browser Window (Always visible background) -->
              <div class="absolute inset-0 bg-white">
                <!-- Browser Header -->
                <div
                  class="flex items-center justify-between border-b bg-gradient-to-r from-slate-200 to-slate-300 p-3 shadow-sm">
                  <div class="flex items-center space-x-2">
                    <div class="h-3 w-3 rounded-full bg-red-500 shadow-sm"></div>
                    <div class="h-3 w-3 rounded-full bg-yellow-500 shadow-sm"></div>
                    <div class="h-3 w-3 rounded-full bg-green-500 shadow-sm"></div>
                  </div>
                  <div class="text-xs font-semibold text-slate-700">
                    Manual Job Hunter's Browser - 47 tabs open
                  </div>
                  <div class="flex items-center space-x-2 text-xs text-slate-500">
                    <div class="h-2 w-2 animate-pulse rounded-full bg-red-500"></div>
                    <span>3:47 AM</span>
                  </div>
                </div>

                <!-- Browser Tabs -->
                <div
                  class="flex flex-wrap gap-1 border-b bg-gradient-to-r from-slate-100 to-slate-200 p-1 shadow-inner">
                  {#each Array(15) as _, i}
                    <div
                      class="max-w-16 truncate rounded-t border-l border-r border-t bg-white px-2 py-1 text-xs shadow-sm
                      {i < 3 ? 'border-red-200 bg-red-50' : 'hover:bg-slate-50'}">
                      {[
                        'Indeed',
                        'LinkedIn',
                        'Glassdoor',
                        'Monster',
                        'ZipRecruiter',
                        'AngelList',
                        'Remote.co',
                        'FlexJobs',
                        'Upwork',
                        'Freelancer',
                        'Fiverr',
                        'Toptal',
                        'Dice',
                        'CareerBuilder',
                        'SimplyHired',
                      ][i]}
                    </div>
                  {/each}
                  <div
                    class="animate-pulse rounded-t bg-gradient-to-r from-red-500 to-red-600 px-3 py-1 text-xs font-bold text-white shadow-lg">
                    +47 more
                  </div>
                </div>

                <!-- Browser Content with Sticky Notes -->
                <div
                  class="relative h-full bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 p-4">
                  <!-- Error Messages and Chaos -->
                  <div class="mb-4 space-y-3">
                    <div
                      class="rounded-lg border border-red-300 bg-gradient-to-r from-red-50 to-red-100 p-3 shadow-md">
                      <div class="flex items-center text-red-700">
                        <X class="mr-2 h-4 w-4" />
                        <div>
                          <div class="font-semibold">
                            Application Error - Session timeout after 2 hours
                          </div>
                          <div class="mt-1 text-xs text-red-600">
                            Lost all form data. Need to start over...
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      class="rounded-lg border border-yellow-300 bg-gradient-to-r from-yellow-50 to-yellow-100 p-3 shadow-md">
                      <div class="text-yellow-800">
                        <div class="font-semibold">
                          📄 Cover_Letter_Template_v47_FINAL_FINAL_USE_THIS.docx
                        </div>
                        <div class="mt-1 text-xs text-yellow-700">Last modified: 3 hours ago</div>
                      </div>
                    </div>
                    <div
                      class="rounded-lg border border-orange-300 bg-gradient-to-r from-orange-50 to-orange-100 p-3 shadow-md">
                      <div class="text-orange-800">
                        <div class="font-semibold">
                          📊 Job_Applications_Tracker_2024_UPDATED_v3_REAL.xlsx
                        </div>
                        <div class="mt-1 text-xs text-orange-700">
                          Status: Completely out of sync
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Lots of Sticky Notes scattered everywhere -->
                  <div
                    class="absolute left-6 top-16 z-10 -rotate-3 transform rounded bg-yellow-300 p-2 text-xs shadow-md">
                    🔔 Follow up Google!
                  </div>
                  <div
                    class="absolute right-8 top-20 z-10 rotate-2 transform rounded bg-pink-300 p-2 text-xs shadow-md">
                    ✏️ Update resume
                  </div>
                  <div
                    class="absolute left-12 top-32 z-10 -rotate-1 transform rounded bg-blue-300 p-2 text-xs shadow-md">
                    📝 Applied to 5 jobs
                  </div>
                  <div
                    class="absolute right-16 top-40 z-10 rotate-3 transform rounded bg-green-300 p-2 text-xs shadow-md">
                    📚 Interview prep
                  </div>
                  <div
                    class="absolute left-1/3 top-24 z-10 rotate-1 transform rounded bg-purple-300 p-2 text-xs shadow-md">
                    � Salary research
                  </div>
                  <div
                    class="absolute bottom-20 left-8 z-10 -rotate-2 transform rounded bg-orange-300 p-2 text-xs shadow-md">
                    📞 Call recruiter
                  </div>
                  <div
                    class="absolute bottom-16 right-12 z-10 rotate-1 transform rounded bg-red-300 p-2 text-xs shadow-md">
                    ⏰ Deadline today!
                  </div>
                  <div
                    class="absolute left-1/4 top-1/2 z-10 -rotate-1 transform rounded bg-cyan-300 p-2 text-xs shadow-md">
                    🎯 Target companies
                  </div>
                  <div
                    class="absolute right-1/4 top-1/2 z-10 rotate-2 transform rounded bg-lime-300 p-2 text-xs shadow-md">
                    📊 Track applications
                  </div>
                  <div
                    class="absolute bottom-1/3 left-1/2 z-10 -rotate-3 transform rounded bg-indigo-300 p-2 text-xs shadow-md">
                    🔍 Job search tips
                  </div>

                  <!-- Stress Indicator -->
                  <div
                    class="absolute bottom-4 left-4 right-4 rounded border border-red-300 bg-red-100 p-2">
                    <div class="flex items-center text-xs text-red-700">
                      <Clock class="mr-1 h-3 w-3" />
                      <span class="font-semibold">6 hours • 3 applications • 0 responses</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Slider Overlay (Automation Dashboard) -->
              <div
                class="absolute inset-0 bg-gradient-to-br from-slate-800 to-slate-900 transition-transform duration-700 ease-in-out"
                style="transform: translateX({sliderPosition}%)">
                <!-- Dashboard Header -->
                <div
                  class="flex items-center justify-between border-b border-slate-600 bg-slate-700/80 p-2">
                  <div class="flex items-center space-x-2">
                    <div class="h-3 w-3 rounded-full bg-red-500"></div>
                    <div class="h-3 w-3 rounded-full bg-yellow-500"></div>
                    <div class="h-3 w-3 rounded-full bg-green-500"></div>
                  </div>
                  <div class="text-xs font-medium text-white">Hirli AI Automation Dashboard</div>
                  <div class="flex items-center space-x-1 text-xs text-white/70">
                    <div class="h-2 w-2 animate-pulse rounded-full bg-green-500"></div>
                    <span>3:47 AM</span>
                  </div>
                </div>

                <!-- Dashboard Content -->
                <div class="h-full bg-gradient-to-br from-slate-800 to-slate-900 p-4">
                  <!-- Stats Cards -->
                  <div class="mb-4 grid grid-cols-3 gap-2">
                    <div
                      class="rounded-lg border border-green-200 bg-gradient-to-br from-green-50 to-green-100 p-3 text-center">
                      <div class="text-lg font-bold text-green-600">247</div>
                      <div class="text-xs font-medium text-green-700">Applications</div>
                    </div>
                    <div
                      class="rounded-lg border border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100 p-3 text-center">
                      <div class="text-lg font-bold text-blue-600">38</div>
                      <div class="text-xs font-medium text-blue-700">Interviews</div>
                    </div>
                    <div
                      class="rounded-lg border border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100 p-3 text-center">
                      <div class="text-lg font-bold text-purple-600">95%</div>
                      <div class="text-xs font-medium text-purple-700">Match Rate</div>
                    </div>
                  </div>

                  <!-- Automation Steps -->
                  <div class="space-y-2">
                    <div
                      class="rounded-lg border border-slate-200/50 bg-white/95 p-3 backdrop-blur-sm
                      {automationStep >= 0
                        ? 'bg-gradient-to-r from-green-50 to-green-100 ring-2 ring-green-300'
                        : ''}">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center text-sm font-medium">
                          <Search class="mr-2 h-4 w-4 text-green-600" />
                          <span>AI Job Scanning</span>
                        </div>
                        <Badge class="bg-green-100 text-xs text-green-700">Active</Badge>
                      </div>
                    </div>

                    <div
                      class="rounded-lg border border-slate-200/50 bg-white/95 p-3 backdrop-blur-sm
                      {automationStep >= 1
                        ? 'bg-gradient-to-r from-blue-50 to-blue-100 ring-2 ring-blue-300'
                        : ''}">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center text-sm font-medium">
                          <Target class="mr-2 h-4 w-4 text-blue-600" />
                          <span>Smart Matching</span>
                        </div>
                        <Badge class="bg-blue-100 text-xs text-blue-700">Processing</Badge>
                      </div>
                    </div>

                    <div
                      class="rounded-lg border border-slate-200/50 bg-white/95 p-3 backdrop-blur-sm
                      {automationStep >= 2
                        ? 'bg-gradient-to-r from-purple-50 to-purple-100 ring-2 ring-purple-300'
                        : ''}">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center text-sm font-medium">
                          <FileText class="mr-2 h-4 w-4 text-purple-600" />
                          <span>Application Generation</span>
                        </div>
                        <Badge class="bg-purple-100 text-xs text-purple-700">AI-Powered</Badge>
                      </div>
                    </div>

                    <div
                      class="rounded-lg border border-slate-200/50 bg-white/95 p-3 backdrop-blur-sm
                      {automationStep >= 3
                        ? 'bg-gradient-to-r from-green-50 to-green-100 ring-2 ring-green-300'
                        : ''}">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center text-sm font-medium">
                          <Send class="mr-2 h-4 w-4 text-green-600" />
                          <span>Bulk Submission</span>
                        </div>
                        <Badge class="bg-green-100 text-xs text-green-700">Complete</Badge>
                      </div>
                    </div>
                  </div>

                  <!-- Success Indicator -->
                  <div
                    class="absolute bottom-4 left-4 right-4 rounded-lg border border-green-300 bg-gradient-to-r from-green-100 to-green-200 p-2">
                    <div class="flex items-center text-green-800">
                      <Zap class="mr-2 h-4 w-4" />
                      <span class="text-xs font-bold"
                        >15 minutes • 50+ applications • 15% response rate</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Interactive Slider Handle -->
              <div
                class="group absolute bottom-0 top-0 z-20 w-1 cursor-col-resize bg-white/80 shadow-lg backdrop-blur-sm"
                style="left: {sliderPosition}%"
                on:mousedown={startDrag}
                on:mousemove={handleSliderDrag}
                on:mouseup={stopDrag}
                on:mouseleave={stopDrag}
                role="slider"
                tabindex="0"
                aria-label="Drag to compare manual vs automated job hunting">
                <!-- Slider Handle -->
                <div
                  class="absolute left-1/2 top-1/2 flex h-8 w-8 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full border-2 border-slate-300 bg-white shadow-xl transition-transform group-hover:scale-110">
                  <ArrowLeftRight class="h-4 w-4 text-slate-600" />
                </div>

                <!-- Labels -->
                <div
                  class="absolute -top-8 left-1/2 -translate-x-1/2 whitespace-nowrap rounded bg-black/70 px-2 py-1 text-xs font-medium text-white">
                  Drag to compare
                </div>
              </div>
            </div>
          </div>

          <!-- Comparison Labels -->
          <div class="mt-6 flex justify-between text-center">
            <div class="flex-1">
              <div class="mb-2 text-lg font-bold text-red-600">Manual Job Hunting</div>
              <div class="text-sm text-slate-600 dark:text-slate-400">
                Chaotic • Time-consuming • Stressful
              </div>
            </div>
            <div class="flex-1">
              <div class="mb-2 text-lg font-bold text-green-600">AI Automation</div>
              <div class="text-sm text-slate-600 dark:text-slate-400">
                Organized • Efficient • Results-driven
              </div>
            </div>
          </div>
        </div>
      {/if}
    </div>
  </div>

  <!-- Modern CTA Section -->
  {#if visible}
    <div class="px-4 pb-20 text-center">
      <div in:fly={{ y: 30, duration: 1000, delay: 800 }} class="mb-12">
        <h3
          class="mb-4 bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-3xl font-bold text-transparent md:text-4xl dark:from-white dark:to-slate-300">
          Ready to transform your job search?
        </h3>
        <p class="mx-auto max-w-2xl text-lg text-slate-600 dark:text-slate-400">
          Join thousands of job seekers who've made the switch from chaos to automation
        </p>
      </div>

      <div
        in:fly={{ y: 30, duration: 1000, delay: 1000 }}
        class="mb-12 flex flex-col justify-center space-y-4 sm:flex-row sm:space-x-6 sm:space-y-0">
        <Button
          class="rounded-xl bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-4 text-lg font-semibold text-white shadow-lg shadow-blue-500/25 transition-all duration-300 hover:scale-105 hover:from-blue-700 hover:to-purple-700 hover:shadow-xl hover:shadow-blue-500/40">
          Start Automating Now
        </Button>
        <Button
          variant="outline"
          class="rounded-xl border-2 border-slate-300 px-8 py-4 text-lg font-semibold transition-all duration-300 hover:border-slate-400 hover:bg-slate-50 dark:hover:bg-slate-800">
          Watch Demo
        </Button>
      </div>

      <div
        in:fly={{ y: 30, duration: 1000, delay: 1200 }}
        class="flex items-center justify-center text-sm text-slate-500 dark:text-slate-400">
        <div class="mr-4 flex -space-x-2">
          <img
            src="https://randomuser.me/api/portraits/women/79.jpg"
            alt="User"
            class="h-10 w-10 rounded-full border-2 border-white shadow-lg" />
          <img
            src="https://randomuser.me/api/portraits/men/32.jpg"
            alt="User"
            class="h-10 w-10 rounded-full border-2 border-white shadow-lg" />
          <img
            src="https://randomuser.me/api/portraits/women/44.jpg"
            alt="User"
            class="h-10 w-10 rounded-full border-2 border-white shadow-lg" />
        </div>
        <span class="font-medium"
          >Join <span class="font-bold text-slate-700 dark:text-slate-300">10,000+</span> successful
          job seekers</span>
      </div>
    </div>
  {/if}
</section>
