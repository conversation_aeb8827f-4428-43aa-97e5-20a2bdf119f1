<script>
  import { fly } from 'svelte/transition';
  import { But<PERSON> } from '$lib/components/ui/button';
  import { Badge } from '$lib/components/ui/badge';
  import {
    Clock,
    Target,
    Zap,
    CheckCircle,
    X,
    Search,
    FileText,
    Send,
    ArrowLeftRight
  } from 'lucide-svelte';

  let visible = $state(false);
  let automationStep = $state(0);
  let sliderPosition = $state(100); // Start fully showing chaos (100% = slider all the way right)
  let isDragging = $state(false);

  $effect(() => {
    visible = true;

    // Automation step animations
    const automationTimer = setInterval(() => {
      automationStep = (automationStep + 1) % 4;
    }, 2500);

    // Auto-slide demo every 6 seconds
    const slideTimer = setInterval(() => {
      if (!isDragging) {
        sliderPosition = sliderPosition === 100 ? 20 : 100;
      }
    }, 6000);

    return () => {
      clearInterval(automationTimer);
      clearInterval(slideTimer);
    };
  });

  // Handle slider drag
  function handleSliderDrag(event) {
    if (!isDragging) return;

    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    sliderPosition = percentage;
  }

  function startDrag() {
    isDragging = true;
  }

  function stopDrag() {
    isDragging = false;
  }
</script>

<section
  class="relative min-h-screen overflow-hidden bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
  <div class="relative z-10 flex min-h-screen flex-col">
    <!-- Main heading -->
    {#if visible}
      <div class="pb-12 pt-20 text-center">
        <div
          in:fly={{ y: 30, duration: 1000 }}
          class="mb-8 text-5xl font-bold tracking-tight md:text-6xl lg:text-7xl">
          Stop <span class="bg-gradient-to-r from-red-500 to-red-600 bg-clip-text text-transparent">struggling</span> with job hunting.
          <br />
          Start <span class="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">automating</span> your success.
        </div>
        <p
          in:fly={{ y: 30, duration: 1000, delay: 200 }}
          class="mx-auto max-w-4xl text-xl font-light leading-relaxed text-slate-600 md:text-2xl dark:text-slate-300">
          Drag the slider to see the transformation from chaos to automation
        </p>
      </div>
    {/if}

    <!-- Desktop Monitor with Slider -->
    <div class="flex-1 px-4 lg:px-8">
      {#if visible}
        <div in:fly={{ y: 50, duration: 1000, delay: 400 }} class="relative mx-auto max-w-5xl">

          <!-- Monitor Stand -->
          <div class="flex flex-col items-center mb-4">
            <div class="w-32 h-6 bg-gradient-to-b from-slate-400 to-slate-500 rounded-lg shadow-lg"></div>
            <div class="w-48 h-3 bg-gradient-to-b from-slate-500 to-slate-600 rounded-b-lg shadow-xl"></div>
          </div>

          <!-- Monitor Screen -->
          <div class="relative bg-black rounded-2xl p-3 shadow-2xl border-4 border-slate-800" style="aspect-ratio: 16/10;">
            <!-- Screen Content -->
            <div class="relative w-full h-full rounded-lg overflow-hidden bg-slate-100">

              <!-- Browser Window (Always visible background) -->
              <div class="absolute inset-0 bg-white">
                <!-- Browser Header -->
                <div class="bg-slate-200 p-2 flex items-center justify-between border-b">
                  <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                  </div>
                  <div class="text-xs text-slate-600 font-medium">Manual Job Hunter's Browser</div>
                  <div class="text-xs text-slate-500">3:47 AM</div>
                </div>

                <!-- Browser Tabs -->
                <div class="bg-slate-100 p-1 flex flex-wrap gap-1 border-b">
                  {#each Array(12) as _, i}
                    <div class="bg-white px-2 py-1 text-xs rounded-t border-l border-r border-t truncate max-w-20">
                      {['Indeed', 'LinkedIn', 'Glassdoor', 'Monster', 'ZipRecruiter', 'AngelList', 'Remote.co', 'FlexJobs', 'Upwork', 'Freelancer', 'Fiverr', 'Toptal'][i]}
                    </div>
                  {/each}
                  <div class="bg-red-500 text-white px-2 py-1 text-xs rounded-t">+47 more</div>
                </div>

                <!-- Browser Content with Sticky Notes -->
                <div class="relative p-4 h-full bg-gradient-to-br from-blue-50 to-purple-50">
                  <!-- Error Messages -->
                  <div class="space-y-2 mb-4">
                    <div class="bg-red-50 border border-red-200 p-2 rounded text-xs">
                      <div class="flex items-center text-red-600">
                        <X class="w-3 h-3 mr-1" />
                        Application Error - Form timeout
                      </div>
                    </div>
                    <div class="bg-yellow-50 border border-yellow-200 p-2 rounded text-xs">
                      <div class="text-yellow-700">📄 Cover_Letter_v47_FINAL.docx</div>
                    </div>
                  </div>

                  <!-- Lots of Sticky Notes scattered everywhere -->
                  <div class="absolute top-16 left-6 bg-yellow-300 p-2 text-xs rounded transform -rotate-3 shadow-md z-10">
                    🔔 Follow up Google!
                  </div>
                  <div class="absolute top-20 right-8 bg-pink-300 p-2 text-xs rounded transform rotate-2 shadow-md z-10">
                    ✏️ Update resume
                  </div>
                  <div class="absolute top-32 left-12 bg-blue-300 p-2 text-xs rounded transform -rotate-1 shadow-md z-10">
                    📝 Applied to 5 jobs
                  </div>
                  <div class="absolute top-40 right-16 bg-green-300 p-2 text-xs rounded transform rotate-3 shadow-md z-10">
                    📚 Interview prep
                  </div>
                  <div class="absolute top-24 left-1/3 bg-purple-300 p-2 text-xs rounded transform rotate-1 shadow-md z-10">
                    � Salary research
                  </div>
                  <div class="absolute bottom-20 left-8 bg-orange-300 p-2 text-xs rounded transform -rotate-2 shadow-md z-10">
                    📞 Call recruiter
                  </div>
                  <div class="absolute bottom-16 right-12 bg-red-300 p-2 text-xs rounded transform rotate-1 shadow-md z-10">
                    ⏰ Deadline today!
                  </div>
                  <div class="absolute top-1/2 left-1/4 bg-cyan-300 p-2 text-xs rounded transform -rotate-1 shadow-md z-10">
                    🎯 Target companies
                  </div>
                  <div class="absolute top-1/2 right-1/4 bg-lime-300 p-2 text-xs rounded transform rotate-2 shadow-md z-10">
                    📊 Track applications
                  </div>
                  <div class="absolute bottom-1/3 left-1/2 bg-indigo-300 p-2 text-xs rounded transform -rotate-3 shadow-md z-10">
                    🔍 Job search tips
                  </div>

                  <!-- Stress Indicator -->
                  <div class="absolute bottom-4 left-4 right-4 bg-red-100 border border-red-300 rounded p-2">
                    <div class="flex items-center text-red-700 text-xs">
                      <Clock class="w-3 h-3 mr-1" />
                      <span class="font-semibold">6 hours • 3 applications • 0 responses</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Slider Overlay (Automation Dashboard) -->
              <div
                class="absolute inset-0 bg-gradient-to-br from-slate-800 to-slate-900 transition-transform duration-700 ease-in-out"
                style="transform: translateX({sliderPosition}%)">

                <!-- Dashboard Header -->
                <div class="bg-slate-700/80 p-2 flex items-center justify-between border-b border-slate-600">
                  <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                  </div>
                  <div class="text-xs text-white font-medium">Hirli AI Automation Dashboard</div>
                  <div class="flex items-center space-x-1 text-xs text-white/70">
                    <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span>3:47 AM</span>
                  </div>
                </div>

                <!-- Dashboard Content -->
                <div class="p-4 h-full bg-gradient-to-br from-slate-800 to-slate-900">
                  <!-- Stats Cards -->
                  <div class="grid grid-cols-3 gap-2 mb-4">
                    <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-3 text-center border border-green-200">
                      <div class="text-lg font-bold text-green-600">247</div>
                      <div class="text-xs font-medium text-green-700">Applications</div>
                    </div>
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-3 text-center border border-blue-200">
                      <div class="text-lg font-bold text-blue-600">38</div>
                      <div class="text-xs font-medium text-blue-700">Interviews</div>
                    </div>
                    <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-3 text-center border border-purple-200">
                      <div class="text-lg font-bold text-purple-600">95%</div>
                      <div class="text-xs font-medium text-purple-700">Match Rate</div>
                    </div>
                  </div>

                  <!-- Automation Steps -->
                  <div class="space-y-2">
                    <div class="bg-white/95 rounded-lg p-3 border border-slate-200/50 backdrop-blur-sm
                      {automationStep >= 0 ? 'ring-2 ring-green-300 bg-gradient-to-r from-green-50 to-green-100' : ''}">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center text-sm font-medium">
                          <Search class="w-4 h-4 mr-2 text-green-600" />
                          <span>AI Job Scanning</span>
                        </div>
                        <Badge class="bg-green-100 text-green-700 text-xs">Active</Badge>
                      </div>
                    </div>

                    <div class="bg-white/95 rounded-lg p-3 border border-slate-200/50 backdrop-blur-sm
                      {automationStep >= 1 ? 'ring-2 ring-blue-300 bg-gradient-to-r from-blue-50 to-blue-100' : ''}">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center text-sm font-medium">
                          <Target class="w-4 h-4 mr-2 text-blue-600" />
                          <span>Smart Matching</span>
                        </div>
                        <Badge class="bg-blue-100 text-blue-700 text-xs">Processing</Badge>
                      </div>
                    </div>

                    <div class="bg-white/95 rounded-lg p-3 border border-slate-200/50 backdrop-blur-sm
                      {automationStep >= 2 ? 'ring-2 ring-purple-300 bg-gradient-to-r from-purple-50 to-purple-100' : ''}">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center text-sm font-medium">
                          <FileText class="w-4 h-4 mr-2 text-purple-600" />
                          <span>Application Generation</span>
                        </div>
                        <Badge class="bg-purple-100 text-purple-700 text-xs">AI-Powered</Badge>
                      </div>
                    </div>

                    <div class="bg-white/95 rounded-lg p-3 border border-slate-200/50 backdrop-blur-sm
                      {automationStep >= 3 ? 'ring-2 ring-green-300 bg-gradient-to-r from-green-50 to-green-100' : ''}">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center text-sm font-medium">
                          <Send class="w-4 h-4 mr-2 text-green-600" />
                          <span>Bulk Submission</span>
                        </div>
                        <Badge class="bg-green-100 text-green-700 text-xs">Complete</Badge>
                      </div>
                    </div>
                  </div>

                  <!-- Success Indicator -->
                  <div class="absolute bottom-4 left-4 right-4 bg-gradient-to-r from-green-100 to-green-200 rounded-lg p-2 border border-green-300">
                    <div class="flex items-center text-green-800">
                      <Zap class="w-4 h-4 mr-2" />
                      <span class="text-xs font-bold">15 minutes • 50+ applications • 15% response rate</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Interactive Slider Handle -->
              <div
                class="absolute top-0 bottom-0 w-1 bg-white/80 backdrop-blur-sm shadow-lg cursor-col-resize z-20 group"
                style="left: {sliderPosition}%"
                on:mousedown={startDrag}
                on:mousemove={handleSliderDrag}
                on:mouseup={stopDrag}
                on:mouseleave={stopDrag}
                role="slider"
                tabindex="0"
                aria-label="Drag to compare manual vs automated job hunting">

                <!-- Slider Handle -->
                <div class="absolute top-1/2 -translate-y-1/2 -translate-x-1/2 left-1/2 w-8 h-8 bg-white rounded-full shadow-xl border-2 border-slate-300 flex items-center justify-center group-hover:scale-110 transition-transform">
                  <ArrowLeftRight class="w-4 h-4 text-slate-600" />
                </div>

                <!-- Labels -->
                <div class="absolute -top-8 left-1/2 -translate-x-1/2 text-xs font-medium text-white bg-black/70 px-2 py-1 rounded whitespace-nowrap">
                  Drag to compare
                </div>
              </div>
            </div>
          </div>

            <!-- Comparison Labels -->
            <div class="flex justify-between mt-6 text-center">
              <div class="flex-1">
                <div class="text-lg font-bold text-red-600 mb-2">Manual Job Hunting</div>
                <div class="text-sm text-slate-600 dark:text-slate-400">Chaotic • Time-consuming • Stressful</div>
              </div>
              <div class="flex-1">
                <div class="text-lg font-bold text-green-600 mb-2">AI Automation</div>
                <div class="text-sm text-slate-600 dark:text-slate-400">Organized • Efficient • Results-driven</div>
              </div>
            </div>
          </div>
        {/if}
      </div>
    </div>

    <!-- Modern CTA Section -->
    {#if visible}
      <div class="text-center pb-20 px-4">
        <div
          in:fly={{ y: 30, duration: 1000, delay: 800 }}
          class="mb-12">
          <h3 class="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-slate-900 to-slate-700 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
            Ready to transform your job search?
          </h3>
          <p class="text-lg text-slate-600 dark:text-slate-400 max-w-2xl mx-auto">
            Join thousands of job seekers who've made the switch from chaos to automation
          </p>
        </div>

        <div
          in:fly={{ y: 30, duration: 1000, delay: 1000 }}
          class="flex flex-col space-y-4 sm:flex-row sm:space-x-6 sm:space-y-0 justify-center mb-12">
          <Button
            class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg shadow-blue-500/25 transition-all duration-300 hover:shadow-xl hover:shadow-blue-500/40 hover:scale-105">
            Start Automating Now
          </Button>
          <Button
            variant="outline"
            class="border-2 border-slate-300 hover:border-slate-400 px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-300 hover:bg-slate-50 dark:hover:bg-slate-800">
            Watch Demo
          </Button>
        </div>

        <div
          in:fly={{ y: 30, duration: 1000, delay: 1200 }}
          class="flex items-center justify-center text-sm text-slate-500 dark:text-slate-400">
          <div class="mr-4 flex -space-x-2">
            <img
              src="https://randomuser.me/api/portraits/women/79.jpg"
              alt="User"
              class="h-10 w-10 rounded-full border-2 border-white shadow-lg" />
            <img
              src="https://randomuser.me/api/portraits/men/32.jpg"
              alt="User"
              class="h-10 w-10 rounded-full border-2 border-white shadow-lg" />
            <img
              src="https://randomuser.me/api/portraits/women/44.jpg"
              alt="User"
              class="h-10 w-10 rounded-full border-2 border-white shadow-lg" />
          </div>
          <span class="font-medium">Join <span class="font-bold text-slate-700 dark:text-slate-300">10,000+</span> successful job seekers</span>
        </div>
      </div>
    {/if}
  </div>
</section>
