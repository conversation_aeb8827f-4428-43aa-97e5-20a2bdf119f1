<script>
  import { fly, fade } from 'svelte/transition';
  import { But<PERSON> } from '$lib/components/ui/button';
  import { Badge } from '$lib/components/ui/badge';
  import {
    Clock,
    Target,
    Zap,
    CheckCircle,
    X,
    Search,
    FileText,
    Send,
    Monitor,
    Wifi,
    Battery,
    Volume2,
    Calendar,
    Mail,
    Folder,
    Chrome,
    Plus,
    Minus,
    RotateCcw,
    Star,
    Bell,
    Settings,
  } from 'lucide-svelte';

  let visible = $state(false);
  let automationStep = $state(0);
  let chaosAnimations = $state([]);

  // Simulate chaos animations and automation process
  $effect(() => {
    visible = true;

    // Chaos animations for the "before" side
    const chaosTimer = setInterval(() => {
      chaosAnimations = [...chaosAnimations, Date.now()].slice(-3);
    }, 1500);

    // Automation step animations for the "after" side
    const automationTimer = setInterval(() => {
      automationStep = (automationStep + 1) % 4;
    }, 2500);

    return () => {
      clearInterval(chaosTimer);
      clearInterval(automationTimer);
    };
  });
</script>

<section class="bg-background text-foreground relative overflow-hidden border-b py-16 md:py-24">
  <div class="container relative z-10 mx-auto px-4">
    <!-- Main heading -->
    {#if visible}
      <div class="mb-16 text-center">
        <div
          in:fly={{ y: 20, duration: 800 }}
          class="mb-6 text-4xl font-light md:text-5xl lg:text-6xl">
          Stop <span class="text-red-500">struggling</span> with job hunting.
          <br />
          Start <span class="gradient-text">automating</span> your success.
        </div>
        <p
          in:fly={{ y: 20, duration: 800, delay: 200 }}
          class="text-muted-foreground mx-auto max-w-3xl text-xl md:text-2xl">
          See the dramatic difference between manual chaos and AI automation
        </p>
      </div>
    {/if}

    <!-- Split-Screen Desktop Mockup -->
    <div class="mb-16 grid grid-cols-1 gap-8 lg:grid-cols-2">
      <!-- BEFORE: Chaotic Desktop -->
      <div class="relative">
        {#if visible}
          <div in:fly={{ x: -50, duration: 800, delay: 400 }} class="relative">
            <!-- Desktop Frame -->
            <div class="rounded-lg bg-gray-900 p-4 shadow-2xl">
              <!-- Desktop Header -->
              <div class="mb-4 flex items-center justify-between rounded bg-gray-800 p-2">
                <div class="flex items-center space-x-2">
                  <div class="h-3 w-3 rounded-full bg-red-500"></div>
                  <div class="h-3 w-3 rounded-full bg-yellow-500"></div>
                  <div class="h-3 w-3 rounded-full bg-green-500"></div>
                </div>
                <div class="text-xs text-white">Manual Job Hunter's Desktop</div>
                <div class="flex items-center space-x-1 text-xs text-white">
                  <span>2:47 AM</span>
                </div>
              </div>

              <!-- Chaotic Browser Tabs -->
              <div class="mb-3 rounded bg-white p-2">
                <div class="mb-2 flex flex-wrap gap-1">
                  {#each Array(12) as _, i}
                    <div
                      class="w-20 truncate rounded bg-gray-200 px-2 py-1 text-xs text-gray-700
                      {chaosAnimations.includes(i) ? 'animate-pulse bg-red-200' : ''}">
                      {[
                        'Indeed',
                        'LinkedIn',
                        'Glassdoor',
                        'Monster',
                        'ZipRecruiter',
                        'AngelList',
                        'Remote.co',
                        'FlexJobs',
                        'Upwork',
                        'Freelancer',
                        'Fiverr',
                        'Toptal',
                      ][i]}
                    </div>
                  {/each}
                  <div class="rounded bg-red-500 px-2 py-1 text-xs text-white">+47 more</div>
                </div>

                <!-- Messy Content -->
                <div class="space-y-2">
                  <div class="rounded border border-red-200 bg-red-50 p-2 text-xs">
                    <div class="mb-1 flex items-center text-red-600">
                      <X class="mr-1 h-3 w-3" />
                      Application Error - Form timeout
                    </div>
                  </div>

                  <div class="rounded border border-yellow-200 bg-yellow-50 p-2 text-xs">
                    <div class="text-yellow-700">Cover letter template v47_final_FINAL.docx</div>
                  </div>

                  <div class="rounded border border-orange-200 bg-orange-50 p-2 text-xs">
                    <div class="text-orange-700">Job_Applications_Tracker_2024_v3.xlsx</div>
                  </div>
                </div>
              </div>

              <!-- Sticky Notes Chaos -->
              <div class="relative h-32">
                <div
                  class="absolute left-0 top-0 -rotate-3 transform rounded bg-yellow-300 p-2 text-xs shadow">
                  Remember to follow up with Google!
                </div>
                <div
                  class="absolute right-0 top-4 rotate-2 transform rounded bg-pink-300 p-2 text-xs shadow">
                  Update resume skills section
                </div>
                <div
                  class="absolute bottom-0 left-8 -rotate-1 transform rounded bg-blue-300 p-2 text-xs shadow">
                  Applied to 5 jobs today
                </div>
                <div
                  class="absolute bottom-2 right-4 rotate-3 transform rounded bg-green-300 p-2 text-xs shadow">
                  Interview prep notes
                </div>
              </div>

              <!-- Stress Indicator -->
              <div class="mt-4 rounded border border-red-300 bg-red-100 p-3">
                <div class="flex items-center text-sm text-red-700">
                  <Clock class="mr-2 h-4 w-4" />
                  <span class="font-semibold">6 hours spent, 3 applications submitted</span>
                </div>
              </div>
            </div>
          </div>
        {/if}
      </div>

      <!-- AFTER: Clean Automation Dashboard -->
      <div class="relative">
        {#if visible}
          <div in:fly={{ x: 50, duration: 800, delay: 600 }} class="relative">
            <!-- Dashboard Frame -->
            <div class="rounded-lg bg-gray-900 p-4 shadow-2xl">
              <!-- Dashboard Header -->
              <div class="mb-4 flex items-center justify-between rounded bg-gray-800 p-2">
                <div class="flex items-center space-x-2">
                  <div class="h-3 w-3 rounded-full bg-red-500"></div>
                  <div class="h-3 w-3 rounded-full bg-yellow-500"></div>
                  <div class="h-3 w-3 rounded-full bg-green-500"></div>
                </div>
                <div class="text-xs text-white">Hirli Automation Dashboard</div>
                <div class="flex items-center space-x-1 text-xs text-white">
                  <span>2:47 AM</span>
                </div>
              </div>

              <!-- Clean Dashboard Interface -->
              <div class="rounded bg-white p-4">
                <!-- Stats Cards -->
                <div class="mb-4 grid grid-cols-3 gap-2">
                  <div class="rounded border border-green-200 bg-green-50 p-2 text-center">
                    <div class="text-lg font-bold text-green-600">247</div>
                    <div class="text-xs text-green-700">Applications Sent</div>
                  </div>
                  <div class="rounded border border-blue-200 bg-blue-50 p-2 text-center">
                    <div class="text-lg font-bold text-blue-600">38</div>
                    <div class="text-xs text-blue-700">Interviews</div>
                  </div>
                  <div class="rounded border border-purple-200 bg-purple-50 p-2 text-center">
                    <div class="text-lg font-bold text-purple-600">95%</div>
                    <div class="text-xs text-purple-700">Match Rate</div>
                  </div>
                </div>

                <!-- Automation Steps -->
                <div class="space-y-2">
                  <div
                    class="flex items-center justify-between rounded p-2
                    {automationStep >= 0 ? 'border border-green-200 bg-green-50' : 'bg-gray-50'}">
                    <div class="flex items-center text-sm">
                      <Search class="mr-2 h-4 w-4 text-green-600" />
                      <span>AI Job Scanning</span>
                    </div>
                    <Badge class="bg-green-100 text-green-700">Active</Badge>
                  </div>

                  <div
                    class="flex items-center justify-between rounded p-2
                    {automationStep >= 1 ? 'border border-blue-200 bg-blue-50' : 'bg-gray-50'}">
                    <div class="flex items-center text-sm">
                      <Target class="mr-2 h-4 w-4 text-blue-600" />
                      <span>Smart Matching</span>
                    </div>
                    <Badge class="bg-blue-100 text-blue-700">Processing</Badge>
                  </div>

                  <div
                    class="flex items-center justify-between rounded p-2
                    {automationStep >= 2 ? 'border border-purple-200 bg-purple-50' : 'bg-gray-50'}">
                    <div class="flex items-center text-sm">
                      <FileText class="mr-2 h-4 w-4 text-purple-600" />
                      <span>Application Generation</span>
                    </div>
                    <Badge class="bg-purple-100 text-purple-700">AI-Powered</Badge>
                  </div>

                  <div
                    class="flex items-center justify-between rounded p-2
                    {automationStep >= 3 ? 'border border-green-200 bg-green-50' : 'bg-gray-50'}">
                    <div class="flex items-center text-sm">
                      <Send class="mr-2 h-4 w-4 text-green-600" />
                      <span>Bulk Submission</span>
                    </div>
                    <Badge class="bg-green-100 text-green-700">Complete</Badge>
                  </div>
                </div>
              </div>

              <!-- Success Indicator -->
              <div class="mt-4 rounded border border-green-300 bg-green-100 p-3">
                <div class="flex items-center text-sm text-green-700">
                  <Zap class="mr-2 h-4 w-4" />
                  <span class="font-semibold">15 minutes, 50+ applications sent automatically</span>
                </div>
              </div>
            </div>
          </div>
        {/if}
      </div>
    </div>

    <!-- CTA Section -->
    {#if visible}
      <div class="text-center">
        <div
          in:fly={{ y: 20, duration: 800, delay: 800 }}
          class="mb-8 flex flex-col justify-center space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
          <Button
            class="bg-primary text-primary-foreground hover:bg-primary/90 px-8 py-4 text-lg font-medium">
            Transform Your Job Search
          </Button>
          <Button variant="outline" class="px-8 py-4 text-lg font-medium">See How It Works</Button>
        </div>

        <div
          in:fly={{ y: 20, duration: 800, delay: 1000 }}
          class="text-muted-foreground flex items-center justify-center text-sm">
          <div class="mr-3 flex -space-x-2">
            <img
              src="https://randomuser.me/api/portraits/women/79.jpg"
              alt="User"
              class="border-background h-8 w-8 rounded-full border-2" />
            <img
              src="https://randomuser.me/api/portraits/men/32.jpg"
              alt="User"
              class="border-background h-8 w-8 rounded-full border-2" />
            <img
              src="https://randomuser.me/api/portraits/women/44.jpg"
              alt="User"
              class="border-background h-8 w-8 rounded-full border-2" />
          </div>
          <span
            >Join <span class="font-semibold">10,000+</span> job seekers who made the switch</span>
        </div>
      </div>
    {/if}
  </div>
</section>
