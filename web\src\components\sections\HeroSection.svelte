<script>
  import { fade, fly } from 'svelte/transition';
  import { But<PERSON> } from '$lib/components/ui/button';
  import { Badge } from '$lib/components/ui/badge';
  import { Clock, Target, Zap, CheckCircle, X, Search, FileText, Send } from 'lucide-svelte';

  let visible = $state(false);
  let animationStep = $state(0);

  // Simulate the automation process
  $effect(() => {
    visible = true;
    const timer = setInterval(() => {
      animationStep = (animationStep + 1) % 4;
    }, 2000);

    return () => clearInterval(timer);
  });
</script>

<section class="bg-background text-foreground relative overflow-hidden border-b py-16 md:py-24">
  <div class="container relative z-10 mx-auto px-4">
    <!-- Main heading -->
    {#if visible}
      <div class="mb-16 text-center">
        <div
          in:fly={{ y: 20, duration: 800 }}
          class="mb-6 text-4xl font-light md:text-5xl lg:text-6xl">
          Stop <span class="text-red-500">struggling</span> with job hunting.
          <br />
          Start <span class="gradient-text">automating</span> your success.
        </div>
        <p
          in:fly={{ y: 20, duration: 800, delay: 200 }}
          class="text-muted-foreground mx-auto max-w-3xl text-xl md:text-2xl">
          See how our AI transforms chaotic job searching into a streamlined, automated process
        </p>
      </div>
    {/if}

    <!-- Before vs After Comparison -->
    <div class="mb-16 grid grid-cols-1 gap-8 lg:grid-cols-2">
      <!-- BEFORE: Manual Job Hunting -->
      <div class="relative">
        {#if visible}
          <div
            in:fly={{ x: -50, duration: 800, delay: 400 }}
            class="h-full rounded-lg border-2 border-red-200 bg-red-50 p-6 dark:border-red-800 dark:bg-red-950/20">
            <div class="mb-6 flex items-center">
              <div class="mr-3 rounded-full bg-red-100 p-2 dark:bg-red-900">
                <X class="h-6 w-6 text-red-600" />
              </div>
              <h3 class="text-2xl font-semibold text-red-700 dark:text-red-400">
                Before: Manual Chaos
              </h3>
            </div>

            <!-- Chaotic job search simulation -->
            <div class="space-y-4">
              <div
                class="flex items-center justify-between rounded border-l-4 border-red-400 bg-white p-3 dark:bg-gray-800">
                <span class="text-sm">Indeed.com - 47 tabs open</span>
                <Badge class="bg-red-100 text-red-700">Overwhelmed</Badge>
              </div>

              <div
                class="flex items-center justify-between rounded border-l-4 border-orange-400 bg-white p-3 dark:bg-gray-800">
                <span class="text-sm">LinkedIn - Manual applications</span>
                <Badge class="bg-orange-100 text-orange-700">3 hrs/day</Badge>
              </div>

              <div
                class="flex items-center justify-between rounded border-l-4 border-yellow-400 bg-white p-3 dark:bg-gray-800">
                <span class="text-sm">Spreadsheet tracking</span>
                <Badge class="bg-yellow-100 text-yellow-700">Error-prone</Badge>
              </div>

              <div
                class="flex items-center justify-between rounded border-l-4 border-red-400 bg-white p-3 dark:bg-gray-800">
                <span class="text-sm">Custom cover letters</span>
                <Badge class="bg-red-100 text-red-700">30 min each</Badge>
              </div>
            </div>

            <div class="mt-6 rounded bg-red-100 p-4 dark:bg-red-900/30">
              <div class="flex items-center text-red-700 dark:text-red-400">
                <Clock class="mr-2 h-5 w-5" />
                <span class="font-semibold">Result: 5 applications/week, 2% response rate</span>
              </div>
            </div>
          </div>
        {/if}
      </div>

      <!-- AFTER: Automated Success -->
      <div class="relative">
        {#if visible}
          <div
            in:fly={{ x: 50, duration: 800, delay: 600 }}
            class="h-full rounded-lg border-2 border-green-200 bg-green-50 p-6 dark:border-green-800 dark:bg-green-950/20">
            <div class="mb-6 flex items-center">
              <div class="mr-3 rounded-full bg-green-100 p-2 dark:bg-green-900">
                <CheckCircle class="h-6 w-6 text-green-600" />
              </div>
              <h3 class="text-2xl font-semibold text-green-700 dark:text-green-400">
                After: AI Automation
              </h3>
            </div>

            <!-- Automated process simulation -->
            <div class="space-y-4">
              <div
                class="flex items-center justify-between rounded border-l-4 border-green-400 bg-white p-3 dark:bg-gray-800 {animationStep >=
                0
                  ? 'ring-2 ring-green-300'
                  : ''}">
                <div class="flex items-center">
                  <Search class="mr-2 h-4 w-4 text-green-600" />
                  <span class="text-sm">AI scans 1000+ jobs</span>
                </div>
                <Badge class="bg-green-100 text-green-700">Instant</Badge>
              </div>

              <div
                class="flex items-center justify-between rounded border-l-4 border-blue-400 bg-white p-3 dark:bg-gray-800 {animationStep >=
                1
                  ? 'ring-2 ring-blue-300'
                  : ''}">
                <div class="flex items-center">
                  <Target class="mr-2 h-4 w-4 text-blue-600" />
                  <span class="text-sm">Smart matching (95% accuracy)</span>
                </div>
                <Badge class="bg-blue-100 text-blue-700">Automated</Badge>
              </div>

              <div
                class="flex items-center justify-between rounded border-l-4 border-purple-400 bg-white p-3 dark:bg-gray-800 {animationStep >=
                2
                  ? 'ring-2 ring-purple-300'
                  : ''}">
                <div class="flex items-center">
                  <FileText class="mr-2 h-4 w-4 text-purple-600" />
                  <span class="text-sm">Custom applications generated</span>
                </div>
                <Badge class="bg-purple-100 text-purple-700">AI-powered</Badge>
              </div>

              <div
                class="flex items-center justify-between rounded border-l-4 border-green-400 bg-white p-3 dark:bg-gray-800 {animationStep >=
                3
                  ? 'ring-2 ring-green-300'
                  : ''}">
                <div class="flex items-center">
                  <Send class="mr-2 h-4 w-4 text-green-600" />
                  <span class="text-sm">Bulk applications sent</span>
                </div>
                <Badge class="bg-green-100 text-green-700">1-click</Badge>
              </div>
            </div>

            <div class="mt-6 rounded bg-green-100 p-4 dark:bg-green-900/30">
              <div class="flex items-center text-green-700 dark:text-green-400">
                <Zap class="mr-2 h-5 w-5" />
                <span class="font-semibold">Result: 50+ applications/week, 15% response rate</span>
              </div>
            </div>
          </div>
        {/if}
      </div>
    </div>

    <!-- CTA Section -->
    {#if visible}
      <div class="text-center">
        <div
          in:fly={{ y: 20, duration: 800, delay: 800 }}
          class="mb-8 flex flex-col justify-center space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
          <Button
            class="bg-primary text-primary-foreground hover:bg-primary/90 px-8 py-4 text-lg font-medium">
            Transform Your Job Search
          </Button>
          <Button variant="outline" class="px-8 py-4 text-lg font-medium">See How It Works</Button>
        </div>

        <div
          in:fly={{ y: 20, duration: 800, delay: 1000 }}
          class="text-muted-foreground flex items-center justify-center text-sm">
          <div class="mr-3 flex -space-x-2">
            <img
              src="https://randomuser.me/api/portraits/women/79.jpg"
              alt="User"
              class="border-background h-8 w-8 rounded-full border-2" />
            <img
              src="https://randomuser.me/api/portraits/men/32.jpg"
              alt="User"
              class="border-background h-8 w-8 rounded-full border-2" />
            <img
              src="https://randomuser.me/api/portraits/women/44.jpg"
              alt="User"
              class="border-background h-8 w-8 rounded-full border-2" />
          </div>
          <span
            >Join <span class="font-semibold">10,000+</span> job seekers who made the switch</span>
        </div>
      </div>
    {/if}
  </div>
</section>
