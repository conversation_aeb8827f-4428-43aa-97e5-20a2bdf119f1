<script>
  import { fly, fade } from 'svelte/transition';
  import { But<PERSON> } from '$lib/components/ui/button';
  import { Badge } from '$lib/components/ui/badge';
  import {
    Clock,
    Target,
    Zap,
    CheckCircle,
    X,
    Search,
    FileText,
    Send,
    Monitor,
    Wifi,
    Battery,
    Volume2,
    Calendar,
    Mail,
    Folder,
    Chrome,
    Plus,
    Minus,
    RotateCcw,
    Star,
    Bell,
    Settings,
  } from 'lucide-svelte';

  let visible = $state(false);
  let automationStep = $state(0);
  let chaosAnimations = $state([]);

  // Simulate chaos animations and automation process
  $effect(() => {
    visible = true;

    // Chaos animations for the "before" side
    const chaosTimer = setInterval(() => {
      chaosAnimations = [...chaosAnimations, Date.now()].slice(-3);
    }, 1500);

    // Automation step animations for the "after" side
    const automationTimer = setInterval(() => {
      automationStep = (automationStep + 1) % 4;
    }, 2500);

    return () => {
      clearInterval(chaosTimer);
      clearInterval(automationTimer);
    };
  });
</script>

<section
  class="relative min-h-screen overflow-hidden bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
  <!-- Background Elements -->
  <div class="bg-grid-slate-100 dark:bg-grid-slate-800 absolute inset-0 opacity-50"></div>
  <div
    class="absolute inset-0 bg-gradient-to-t from-white/80 via-transparent to-transparent dark:from-slate-900/80">
  </div>

  <div class="relative z-10 flex min-h-screen flex-col">
    <!-- Main heading -->
    {#if visible}
      <div class="pb-12 pt-20 text-center">
        <div
          in:fly={{ y: 30, duration: 1000 }}
          class="mb-8 text-5xl font-bold tracking-tight md:text-6xl lg:text-7xl xl:text-8xl">
          Stop <span class="bg-gradient-to-r from-red-500 to-red-600 bg-clip-text text-transparent"
            >struggling</span>
          with job hunting.
          <br />
          Start
          <span
            class="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent"
            >automating</span> your success.
        </div>
        <p
          in:fly={{ y: 30, duration: 1000, delay: 200 }}
          class="mx-auto max-w-4xl text-xl font-light leading-relaxed text-slate-600 md:text-2xl lg:text-3xl dark:text-slate-300">
          Experience the transformation from chaotic manual job hunting to seamless AI automation
        </p>
      </div>
    {/if}

    <!-- Full-Width Split-Screen Desktop Mockup -->
    <div class="flex-1 px-8 lg:px-16">
      <div class="grid grid-cols-1 gap-12 lg:grid-cols-2 lg:gap-16">
        <!-- BEFORE: Chaotic Desktop -->
        <div class="relative">
          {#if visible}
            <div in:fly={{ x: -60, duration: 1000, delay: 400 }} class="group relative">
              <!-- Modern Desktop Frame -->
              <div
                class="relative rounded-2xl border border-slate-700/50 bg-gradient-to-br from-slate-800 to-slate-900 p-6 shadow-2xl backdrop-blur-sm">
                <!-- Glowing border effect -->
                <div
                  class="absolute inset-0 rounded-2xl bg-gradient-to-r from-red-500/20 to-orange-500/20 opacity-75 blur-xl transition-opacity group-hover:opacity-100">
                </div>

                <!-- Desktop Header -->
                <div
                  class="relative mb-6 flex items-center justify-between rounded-xl border border-slate-600/30 bg-slate-700/50 p-4 backdrop-blur-sm">
                  <div class="flex items-center space-x-3">
                    <div class="h-4 w-4 rounded-full bg-red-500 shadow-lg shadow-red-500/50"></div>
                    <div class="h-4 w-4 rounded-full bg-yellow-500 shadow-lg shadow-yellow-500/50">
                    </div>
                    <div class="h-4 w-4 rounded-full bg-green-500 shadow-lg shadow-green-500/50">
                    </div>
                  </div>
                  <div class="text-sm font-medium text-white/90">Manual Job Hunter's Nightmare</div>
                  <div class="flex items-center space-x-2 text-sm text-white/70">
                    <div class="h-2 w-2 animate-pulse rounded-full bg-red-500"></div>
                    <span>3:47 AM</span>
                  </div>
                </div>

                <!-- Modern Chaotic Browser Interface -->
                <div
                  class="relative mb-4 rounded-xl border border-slate-200/50 bg-white/95 p-4 backdrop-blur-sm">
                  <!-- Browser Tab Bar -->
                  <div class="mb-3 flex flex-wrap gap-1">
                    {#each Array(12) as _, i}
                      <div
                        class="relative overflow-hidden rounded-lg px-3 py-2 text-xs font-medium transition-all duration-300
                        {chaosAnimations.includes(i)
                          ? 'animate-pulse bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg shadow-red-500/50'
                          : 'bg-slate-100 text-slate-700 hover:bg-slate-200'}">
                        <div class="relative z-10">
                          {[
                            'Indeed',
                            'LinkedIn',
                            'Glassdoor',
                            'Monster',
                            'ZipRecruiter',
                            'AngelList',
                            'Remote.co',
                            'FlexJobs',
                            'Upwork',
                            'Freelancer',
                            'Fiverr',
                            'Toptal',
                          ][i]}
                        </div>
                      </div>
                    {/each}
                    <div
                      class="rounded-lg bg-gradient-to-r from-red-600 to-red-700 px-3 py-2 text-xs font-bold text-white shadow-lg shadow-red-600/50">
                      +47 more tabs
                    </div>
                  </div>

                  <!-- Error Messages & Chaos -->
                  <div class="space-y-3">
                    <div
                      class="rounded-lg border border-red-200 bg-gradient-to-r from-red-50 to-red-100 p-3">
                      <div class="flex items-center text-red-700">
                        <X class="mr-2 h-4 w-4" />
                        <span class="text-sm font-medium"
                          >Application Error - Session timeout after 2 hours</span>
                      </div>
                    </div>

                    <div
                      class="rounded-lg border border-yellow-200 bg-gradient-to-r from-yellow-50 to-yellow-100 p-3">
                      <div class="text-sm font-medium text-yellow-800">
                        📄 Cover_Letter_Template_v47_FINAL_FINAL_USE_THIS.docx
                      </div>
                    </div>

                    <div
                      class="rounded-lg border border-orange-200 bg-gradient-to-r from-orange-50 to-orange-100 p-3">
                      <div class="text-sm font-medium text-orange-800">
                        📊 Job_Applications_Tracker_2024_UPDATED_v3_REAL.xlsx
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Modern Sticky Notes -->
                <div class="relative mb-4 h-40">
                  <div
                    class="absolute left-0 top-0 -rotate-3 transform rounded-xl border border-yellow-500/20 bg-gradient-to-br from-yellow-300 to-yellow-400 p-3 text-sm font-medium shadow-xl shadow-yellow-400/30">
                    🔔 Follow up with Google!
                  </div>
                  <div
                    class="absolute right-0 top-4 rotate-2 transform rounded-xl border border-pink-500/20 bg-gradient-to-br from-pink-300 to-pink-400 p-3 text-sm font-medium shadow-xl shadow-pink-400/30">
                    ✏️ Update resume skills
                  </div>
                  <div
                    class="absolute bottom-0 left-8 -rotate-1 transform rounded-xl border border-blue-500/20 bg-gradient-to-br from-blue-300 to-blue-400 p-3 text-sm font-medium shadow-xl shadow-blue-400/30">
                    📝 Applied to 5 jobs today
                  </div>
                  <div
                    class="absolute bottom-2 right-4 rotate-3 transform rounded-xl border border-green-500/20 bg-gradient-to-br from-green-300 to-green-400 p-3 text-sm font-medium shadow-xl shadow-green-400/30">
                    📚 Interview prep notes
                  </div>
                </div>

                <!-- Stress Indicator -->
                <div
                  class="relative rounded-xl border border-red-300 bg-gradient-to-r from-red-100 to-red-200 p-4 shadow-lg">
                  <div class="flex items-center text-red-800">
                    <Clock class="mr-3 h-5 w-5" />
                    <span class="text-sm font-bold"
                      >6 hours spent • 3 applications submitted • 0 responses</span>
                  </div>
                  <div
                    class="absolute -right-1 -top-1 h-3 w-3 animate-pulse rounded-full bg-red-500">
                  </div>
                </div>
              </div>
            </div>
          {/if}
        </div>

        <!-- AFTER: Clean Automation Dashboard -->
        <div class="relative">
          {#if visible}
            <div in:fly={{ x: 60, duration: 1000, delay: 600 }} class="group relative">
              <!-- Modern Dashboard Frame -->
              <div
                class="relative rounded-2xl border border-slate-700/50 bg-gradient-to-br from-slate-800 to-slate-900 p-6 shadow-2xl backdrop-blur-sm">
                <!-- Glowing border effect -->
                <div
                  class="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 opacity-75 blur-xl transition-opacity group-hover:opacity-100">
                </div>

                <!-- Dashboard Header -->
                <div
                  class="relative mb-6 flex items-center justify-between rounded-xl border border-slate-600/30 bg-slate-700/50 p-4 backdrop-blur-sm">
                  <div class="flex items-center space-x-3">
                    <div class="h-4 w-4 rounded-full bg-red-500 shadow-lg shadow-red-500/50"></div>
                    <div class="h-4 w-4 rounded-full bg-yellow-500 shadow-lg shadow-yellow-500/50">
                    </div>
                    <div class="h-4 w-4 rounded-full bg-green-500 shadow-lg shadow-green-500/50">
                    </div>
                  </div>
                  <div class="text-sm font-medium text-white/90">Hirli AI Automation Dashboard</div>
                  <div class="flex items-center space-x-2 text-sm text-white/70">
                    <div class="h-2 w-2 animate-pulse rounded-full bg-green-500"></div>
                    <span>3:47 AM</span>
                  </div>
                </div>

                <!-- Modern Dashboard Interface -->
                <div
                  class="relative rounded-xl border border-slate-200/50 bg-white/95 p-6 backdrop-blur-sm">
                  <!-- Stats Cards -->
                  <div class="mb-6 grid grid-cols-3 gap-3">
                    <div
                      class="rounded-xl border border-green-200 bg-gradient-to-br from-green-50 to-green-100 p-4 text-center shadow-lg shadow-green-500/10">
                      <div class="text-2xl font-bold text-green-600">247</div>
                      <div class="text-xs font-medium text-green-700">Applications Sent</div>
                    </div>
                    <div
                      class="rounded-xl border border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100 p-4 text-center shadow-lg shadow-blue-500/10">
                      <div class="text-2xl font-bold text-blue-600">38</div>
                      <div class="text-xs font-medium text-blue-700">Interviews</div>
                    </div>
                    <div
                      class="rounded-xl border border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100 p-4 text-center shadow-lg shadow-purple-500/10">
                      <div class="text-2xl font-bold text-purple-600">95%</div>
                      <div class="text-xs font-medium text-purple-700">Match Rate</div>
                    </div>
                  </div>

                  <!-- Automation Steps -->
                  <div class="space-y-2">
                    <div
                      class="flex items-center justify-between rounded p-2
                    {automationStep >= 0 ? 'border border-green-200 bg-green-50' : 'bg-gray-50'}">
                      <div class="flex items-center text-sm">
                        <Search class="mr-2 h-4 w-4 text-green-600" />
                        <span>AI Job Scanning</span>
                      </div>
                      <Badge class="bg-green-100 text-green-700">Active</Badge>
                    </div>

                    <div
                      class="flex items-center justify-between rounded p-2
                    {automationStep >= 1 ? 'border border-blue-200 bg-blue-50' : 'bg-gray-50'}">
                      <div class="flex items-center text-sm">
                        <Target class="mr-2 h-4 w-4 text-blue-600" />
                        <span>Smart Matching</span>
                      </div>
                      <Badge class="bg-blue-100 text-blue-700">Processing</Badge>
                    </div>

                    <div
                      class="flex items-center justify-between rounded p-2
                    {automationStep >= 2 ? 'border border-purple-200 bg-purple-50' : 'bg-gray-50'}">
                      <div class="flex items-center text-sm">
                        <FileText class="mr-2 h-4 w-4 text-purple-600" />
                        <span>Application Generation</span>
                      </div>
                      <Badge class="bg-purple-100 text-purple-700">AI-Powered</Badge>
                    </div>

                    <div
                      class="flex items-center justify-between rounded p-2
                    {automationStep >= 3 ? 'border border-green-200 bg-green-50' : 'bg-gray-50'}">
                      <div class="flex items-center text-sm">
                        <Send class="mr-2 h-4 w-4 text-green-600" />
                        <span>Bulk Submission</span>
                      </div>
                      <Badge class="bg-green-100 text-green-700">Complete</Badge>
                    </div>
                  </div>
                </div>

                <!-- Success Indicator -->
                <div class="mt-4 rounded border border-green-300 bg-green-100 p-3">
                  <div class="flex items-center text-sm text-green-700">
                    <Zap class="mr-2 h-4 w-4" />
                    <span class="font-semibold"
                      >15 minutes, 50+ applications sent automatically</span>
                  </div>
                </div>
              </div>
            </div>
          {/if}
        </div>
      </div>

      <!-- CTA Section -->
      {#if visible}
        <div class="text-center">
          <div
            in:fly={{ y: 20, duration: 800, delay: 800 }}
            class="mb-8 flex flex-col justify-center space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
            <Button
              class="bg-primary text-primary-foreground hover:bg-primary/90 px-8 py-4 text-lg font-medium">
              Transform Your Job Search
            </Button>
            <Button variant="outline" class="px-8 py-4 text-lg font-medium"
              >See How It Works</Button>
          </div>

          <div
            in:fly={{ y: 20, duration: 800, delay: 1000 }}
            class="text-muted-foreground flex items-center justify-center text-sm">
            <div class="mr-3 flex -space-x-2">
              <img
                src="https://randomuser.me/api/portraits/women/79.jpg"
                alt="User"
                class="border-background h-8 w-8 rounded-full border-2" />
              <img
                src="https://randomuser.me/api/portraits/men/32.jpg"
                alt="User"
                class="border-background h-8 w-8 rounded-full border-2" />
              <img
                src="https://randomuser.me/api/portraits/women/44.jpg"
                alt="User"
                class="border-background h-8 w-8 rounded-full border-2" />
            </div>
            <span
              >Join <span class="font-semibold">10,000+</span> job seekers who made the switch</span>
          </div>
        </div>
      {/if}
    </div>
  </div>
</section>
