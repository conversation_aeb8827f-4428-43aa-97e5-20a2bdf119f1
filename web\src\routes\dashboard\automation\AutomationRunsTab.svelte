<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input/index.js';
  import * as Card from '$lib/components/ui/card';
  import * as Select from '$lib/components/ui/select';
  import { Badge } from '$lib/components/ui/badge';
  import { Progress } from '$lib/components/ui/progress/index.js';
  import { Slider } from '$lib/components/ui/slider';
  import { Label } from '$lib/components/ui/label';
  import { Switch } from '$lib/components/ui/switch';
  import MultiCombobox from '$lib/components/ui/combobox/multi-combobox.svelte';
  import FeatureGuard from '$components/features/EnhancedFeatureGuard.svelte';
  import { getProfileData } from '$lib/utils/profile';
  import { formatDistance } from 'date-fns';
  import ResolvedKeywords from '$lib/components/automation/ResolvedKeywords.svelte';
  import ResolvedLocations from '$lib/components/automation/ResolvedLocations.svelte';
  import {
    Search,
    Play,
    CheckCircle,
    Clock,
    XCircle,
    StopCircle,
    CircleHelp,
    PlayIcon,
    Plus,
    AlertTriangle,
    X,
  } from 'lucide-svelte';
  import { goto } from '$app/navigation';
  import * as Dialog from '$lib/components/ui/dialog';
  import { ScrollArea } from '$lib/components/ui/scroll-area';

  const {
    userData,
    automationRuns,
    profiles,
    form,
    enhance,
    submitting,
    occupationOptions,
    locationOptions,
    searchOccupations,
    searchLocations,
    isFormValid,
    profileSuggestions,
    applySuggestions,
    checkAutomationEligibility,
    isProfileEligible,
    onRunSelect,
    onCreateRun,
  } = $props<{
    userData: any;
    automationRuns: any;
    profiles: any[];
    form: any;
    enhance: any;
    submitting: any;
    occupationOptions: () => any[];
    locationOptions: () => any[];
    searchOccupations: (search?: string) => Promise<any[]>;
    searchLocations: (search?: string) => Promise<any[]>;
    isFormValid: () => boolean;
    profileSuggestions: () => any;
    applySuggestions: () => void;
    checkAutomationEligibility: (profile: any) => any;
    isProfileEligible: () => boolean;
    onRunSelect: (run: any) => void;
    onCreateRun: () => void;
  }>();

  // Filter and search state
  let runStatusFilter = $state('all');
  let runSearchQuery = $state('');

  // Dialog state
  let createDialogOpen = $state(false);

  // Status filter options
  const statusFilterOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'pending', label: 'Pending' },
    { value: 'start', label: 'Starting' },
    { value: 'in progress', label: 'In Progress' },
    { value: 'running', label: 'Running' },
    { value: 'completed', label: 'Completed' },
    { value: 'failed', label: 'Failed' },
    { value: 'stopped', label: 'Stopped' },
  ];

  // Get current status filter option
  const currentStatusOption = $derived(() => {
    return (
      statusFilterOptions.find((option) => option.value === runStatusFilter) ||
      statusFilterOptions[0]
    );
  });

  // Filtered automation runs
  const filteredAutomationRuns = $derived(() => {
    return automationRuns.filter((run: any) => {
      // Status filter
      if (runStatusFilter !== 'all' && run.status !== runStatusFilter) {
        return false;
      }

      // Search filter
      if (runSearchQuery.trim()) {
        const query = runSearchQuery.toLowerCase();
        const profileName = run.profile ? getProfileData(run.profile).fullName || '' : '';
        const keywords = run.keywords || '';
        const location = run.location || '';

        return (
          profileName.toLowerCase().includes(query) ||
          keywords.toLowerCase().includes(query) ||
          location.toLowerCase().includes(query)
        );
      }

      return true;
    });
  });

  // Helper functions
  function formatDistanceToNow(date: Date | string): string {
    if (!date) return '';
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return formatDistance(dateObj, new Date(), { addSuffix: true });
  }

  function getStatusBadgeVariant(status: string) {
    switch (status) {
      case 'completed':
        return 'default';
      case 'start':
      case 'running':
        return 'secondary';
      case 'failed':
        return 'destructive';
      case 'stopped':
        return 'outline';
      case 'in progress':
      case 'pending':
        return 'outline';
      default:
        return 'outline';
    }
  }

  function getStatusIcon(status: string) {
    switch (status) {
      case 'completed':
        return CheckCircle;
      case 'start':
      case 'running':
        return Play;
      case 'failed':
        return XCircle;
      case 'stopped':
        return StopCircle;
      case 'in progress':
      case 'pending':
        return Clock;
      default:
        return Clock;
    }
  }

  function calculateProgress(run: any): number {
    if (run.status === 'completed') return 100;
    if (run.status === 'failed' || run.status === 'stopped') return run.progress || 0;
    if (run.status === 'start') return 5;
    if (run.status === 'in progress') return run.progress || 50;
    if (run.status === 'running') return run.progress || 50;
    return run.progress || 0;
  }

  function getStatusLabel(status: string): string {
    switch (status) {
      case 'start':
        return 'Starting';
      case 'in progress':
        return 'In Progress';
      case 'running':
        return 'Running';
      case 'completed':
        return 'Completed';
      case 'failed':
        return 'Failed';
      case 'stopped':
        return 'Stopped';
      case 'pending':
        return 'Pending';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  }
</script>

<FeatureGuard
  {userData}
  featureId="automation"
  limitId="automation_runs_per_month"
  showUpgradePrompt={true}
  fallbackMessage="Automation features are not available in your current plan">
  <div class="border-border flex flex-wrap items-center justify-between gap-4 border-b p-2">
    <div class="flex flex-wrap items-center gap-2">
      <div class="relative flex items-center gap-2">
        <Search class="text-muted-foreground absolute left-2.5 top-3 h-4 w-4" />
        <Input
          placeholder="Search runs..."
          class="h-9 w-[200px] pl-9"
          bind:value={runSearchQuery} />
      </div>
      <Select.Root
        type="single"
        value={runStatusFilter}
        onValueChange={(value) => {
          runStatusFilter = value || 'all';
        }}>
        <Select.Trigger class="w-[140px] p-2">
          <Select.Value placeholder={currentStatusOption().label} />
        </Select.Trigger>
        <Select.Content class="w-[140px]">
          {#each statusFilterOptions as option (option.value)}
            <Select.Item value={option.value}>
              {option.label}
            </Select.Item>
          {/each}
        </Select.Content>
      </Select.Root>
    </div>
    <Button size="default" onclick={() => (createDialogOpen = true)}>
      <PlayIcon class="mr-1 h-3 w-3" />
      New Run
    </Button>
  </div>

  <div class="p-2">
    {#if automationRuns.length === 0}
      <div
        class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center">
        <Search class="mb-4 h-12 w-12 text-gray-400" />
        <h3 class="text-xl font-semibold text-gray-300">No automation runs yet</h3>
        <p class="mt-2 text-gray-400">
          Create your first automation run to start searching for jobs
        </p>
        <Button variant="default" onclick={onCreateRun} class="mt-4">
          <Play class="mr-2 h-4 w-4" />
          New Automation Run
        </Button>
      </div>
    {:else if filteredAutomationRuns().length === 0}
      <div
        class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center">
        <Search class="mb-4 h-12 w-12 text-gray-400" />
        <h3 class="text-xl font-semibold text-gray-300">No runs match your filters</h3>
        <p class="mt-2 text-gray-400">Try adjusting your search or filter criteria</p>
      </div>
    {:else}
      <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {#each filteredAutomationRuns() as run (run.id)}
          <Card.Root class="gap-0 overflow-hidden p-0">
            <Card.Header class="border-border border-b !p-4">
              <div class="flex items-center justify-between">
                <Card.Title>
                  {#if run.profile}
                    {getProfileData(run.profile).fullName || 'Unnamed Profile'}
                  {:else}
                    Automation Run
                  {/if}
                </Card.Title>
                <Badge variant={getStatusBadgeVariant(run.status)} class="ml-2">
                  {#if getStatusIcon(run.status)}
                    {@const Icon = getStatusIcon(run.status)}
                    <Icon class="mr-1 h-3 w-3" />
                  {/if}
                  {getStatusLabel(run.status)}
                </Badge>
              </div>
              <Card.Description>
                {#if run.createdAt}
                  Started {formatDistanceToNow(new Date(run.createdAt))} ago
                {/if}
              </Card.Description>
            </Card.Header>

            <Progress value={calculateProgress(run)} max={100} class="rounded-none" />
            <Card.Content class="flex flex-col gap-4 p-4 pt-3">
              <div class="flex flex-row justify-between text-xs">
                <div class="text-primary/50">Progress</div>
                <div class="text-primary/50 text-right">
                  {calculateProgress(run)}% Complete
                </div>
              </div>

              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div class="font-medium text-gray-400">Keywords</div>
                  <div class="truncate">
                    <ResolvedKeywords keywordIds={run.keywords || ''} fallback="None" />
                  </div>
                </div>
                <div>
                  <div class="font-medium text-gray-400">Location</div>
                  <div class="truncate">
                    <ResolvedLocations locationIds={run.location || ''} fallback="None" />
                  </div>
                </div>
              </div>

              <div class="flex flex-col">
                <div class="font-medium text-gray-400">Jobs Found</div>
                <div class="flex items-center gap-2">
                  <span class="text-lg font-semibold"
                    >{run.matchedJobIds?.length || run.jobsFound || 0}</span>
                  {#if ['running', 'pending', 'start', 'in progress'].includes(run.status)}
                    <span class="text-xs text-gray-400">(in progress)</span>
                  {/if}
                </div>
              </div>
            </Card.Content>

            <Card.Footer class="grid grid-cols-2 gap-4 border-t !p-2">
              <Button variant="outline" size="sm" onclick={() => onRunSelect(run)}>
                View Details
              </Button>
              <Button
                variant="outline"
                size="sm"
                onclick={() => goto(`/dashboard/automation/${run.id}`)}>
                Full View
              </Button>
            </Card.Footer>
          </Card.Root>
        {/each}
      </div>
    {/if}
  </div>
</FeatureGuard>

<!-- Create Automation Run Dialog -->
<Dialog.Root bind:open={createDialogOpen}>
  <Dialog.Overlay />
  <Dialog.Content class="max-h-[90vh] max-w-4xl gap-0 p-0">
    <FeatureGuard
      {userData}
      featureId="automation"
      limitId="automation_runs_per_month"
      showUpgradePrompt={true}
      fallbackMessage="Automation features are not available in your current plan">
      <Dialog.Header class="border-border gap-1 border-b p-4">
        <Dialog.Title>Configure Automation Run</Dialog.Title>
        <Dialog.Description>
          Set up detailed automation specifications for intelligent job matching and application.
        </Dialog.Description>
      </Dialog.Header>

      <ScrollArea orientation="vertical" class="max-h-[calc(100vh-200px)] overflow-hidden">
        <form method="POST" use:enhance>
          <!-- Hidden form fields -->
          <input type="hidden" name="profileId" bind:value={$form.profileId} />
          <input type="hidden" name="keywords" value={JSON.stringify($form.keywords)} />
          <input type="hidden" name="locations" value={JSON.stringify($form.locations)} />
          <input type="hidden" name="maxJobsToApply" bind:value={$form.maxJobsToApply} />
          <input type="hidden" name="minMatchScore" bind:value={$form.minMatchScore} />
          <input type="hidden" name="autoApplyEnabled" value={$form.autoApplyEnabled} />
          <input type="hidden" name="salaryRange" value={JSON.stringify($form.salaryRange)} />
          <input
            type="hidden"
            name="experienceRange"
            value={JSON.stringify($form.experienceRange)} />
          <input type="hidden" name="jobTypes" value={JSON.stringify($form.jobTypes)} />
          <input type="hidden" name="remotePreference" bind:value={$form.remotePreference} />
          <input
            type="hidden"
            name="companySizePreference"
            value={JSON.stringify($form.companySizePreference)} />
          <input
            type="hidden"
            name="excludeCompanies"
            value={JSON.stringify($form.excludeCompanies)} />
          <input
            type="hidden"
            name="preferredCompanies"
            value={JSON.stringify($form.preferredCompanies)} />

          <div class="mb-0 grid gap-4 p-4">
            <!-- Profile Selection -->
            <div class="grid gap-1">
              <div class="flex items-center justify-between">
                <label for="profile" class="text-sm font-medium">Profile *</label>
                <Button
                  variant="link"
                  size="sm"
                  onclick={() => goto('/dashboard/settings/profile')}>
                  Manage Profiles
                </Button>
              </div>
              <Select.Root
                type="single"
                value={$form.profileId}
                onValueChange={(value) => {
                  $form.profileId = value || '';
                }}>
                <Select.Trigger class="w-full p-2">
                  <Select.Value
                    placeholder={profiles.find((p) => p.id === $form.profileId)?.name ||
                      'Select a profile'} />
                </Select.Trigger>
                <Select.Content class="max-h-60">
                  {#each profiles as profile (profile.id)}
                    <Select.Item value={profile.id}>
                      {profile.name}
                    </Select.Item>
                  {/each}
                </Select.Content>
              </Select.Root>
            </div>

            <!-- Profile Eligibility Check -->
            {#if $form.profileId}
              {@const selectedProfile = profiles.find((p) => p.id === $form.profileId)}
              {#if selectedProfile}
                {@const eligibility = checkAutomationEligibility(selectedProfile)}
                <div class="rounded-lg border p-4">
                  <div class="mb-2 flex items-center gap-2 text-sm">
                    {#if eligibility.isEligible}
                      <CheckCircle class="h-4 w-4 text-green-500" />
                      <span class=" text-green-700">Profile Eligible for Automation</span>
                    {:else}
                      <AlertTriangle class="h-4 w-4 text-orange-500" />
                      <span class="text-orange-700">Profile Needs Completion</span>
                    {/if}
                  </div>

                  <div class="mb-3">
                    <div class="mb-1 flex items-center justify-between text-sm">
                      <span>Profile Completion</span>
                      <span>{eligibility.completionPercentage}%</span>
                    </div>
                    <Progress value={eligibility.completionPercentage} max={100} />
                  </div>

                  {#if !eligibility.isEligible}
                    <div class="space-y-1">
                      <p class="text-sm font-medium text-gray-700">Missing Requirements:</p>
                      {#each eligibility.missingRequirements as requirement}
                        <div class="flex items-center gap-2 text-sm text-gray-600">
                          <X class="h-3 w-3 text-red-500" />
                          {requirement}
                        </div>
                      {/each}
                    </div>
                  {/if}
                </div>
              {/if}
            {/if}

            <!-- Automation Settings -->
            {#if $form.profileId && isProfileEligible()}
              <!-- Search Criteria Section -->
              <div class="space-y-6">
                <div class="mb-4 flex items-center justify-between">
                  <h4 class="text-md font-light">Search Criteria</h4>
                  {#if profileSuggestions() && profileSuggestions().jobTitles.length > 0}
                    <Button variant="outline" size="sm" onclick={applySuggestions} class="text-xs">
                      <Plus class="mr-1 h-3 w-3" />
                      Use Profile Suggestions
                    </Button>
                  {/if}
                </div>

                <div class="grid gap-6 md:grid-cols-2">
                  <div class="flex flex-col space-y-2">
                    <label for="keywords" class="text-sm font-normal">Job Keywords *</label>
                    <MultiCombobox
                      placeholder="Search for occupations..."
                      selectedValues={$form.keywords}
                      options={occupationOptions()}
                      onSelectedValuesChange={(values) => ($form.keywords = values)}
                      searchOptions={searchOccupations}
                      maxDisplayItems={1}
                      width="w-55" />
                    {#if profileSuggestions() && profileSuggestions().jobTitles.length > 0}
                      <p class="text-muted-foreground text-xs">
                        Suggestions: {profileSuggestions().jobTitles.join(', ')}
                      </p>
                    {/if}
                  </div>

                  <div class="flex flex-col space-y-2">
                    <label for="location" class="text-sm font-normal">Locations</label>
                    <MultiCombobox
                      placeholder="Search for cities..."
                      selectedValues={$form.locations}
                      options={locationOptions()}
                      onSelectedValuesChange={(values) => ($form.locations = values)}
                      searchOptions={searchLocations}
                      maxDisplayItems={1}
                      width="w-55" />
                    {#if profileSuggestions() && profileSuggestions().location}
                      <p class="text-muted-foreground text-xs">
                        From profile: {profileSuggestions().location}
                      </p>
                    {/if}
                  </div>
                </div>

                <!-- Automation Settings Section -->
                <h4 class="text-md mb-4 font-light">Automation Settings</h4>

                <div class="grid gap-6 md:grid-cols-2">
                  <!-- Left Column -->
                  <div class="space-y-6">
                    <div class="space-y-3">
                      <Label class="text-xs font-normal">
                        Maximum Jobs to Apply: <span class="text-primary font-semibold"
                          >{$form.maxJobsToApply}</span>
                      </Label>
                      <Slider
                        type="single"
                        bind:value={$form.maxJobsToApply}
                        min={1}
                        max={50}
                        step={1}
                        class="w-full" />
                      <div class="text-muted-foreground flex justify-between text-xs">
                        <span>1 job</span>
                        <span>50 jobs</span>
                      </div>
                    </div>

                    <div class="space-y-3">
                      <Label class="text-xs font-normal">
                        Minimum Match Score: <span class="text-primary font-semibold"
                          >{$form.minMatchScore}%</span>
                      </Label>
                      <Slider
                        type="single"
                        bind:value={$form.minMatchScore}
                        min={60}
                        max={95}
                        step={5}
                        class="w-full" />
                      <div class="text-muted-foreground flex justify-between text-xs">
                        <span>60%</span>
                        <span>95%</span>
                      </div>
                    </div>
                  </div>

                  <!-- Right Column -->
                  <div class="space-y-6">
                    <div class="space-y-3">
                      <Label class="text-xs font-normal">
                        Salary Range: <span class="text-primary font-semibold"
                          >${$form.salaryRange[0]}k - ${$form.salaryRange[1]}k</span>
                      </Label>
                      <Slider
                        type="multiple"
                        bind:value={$form.salaryRange}
                        min={30}
                        max={250}
                        step={5}
                        class="w-full" />
                      <div class="text-muted-foreground flex justify-between text-xs">
                        <span>$30k</span>
                        <span>$250k+</span>
                      </div>
                    </div>

                    <div class="space-y-3">
                      <Label class="text-xs font-medium">
                        Experience Range: <span class="text-primary font-normal"
                          >{$form.experienceRange[0]} - {$form.experienceRange[1]} years</span>
                      </Label>
                      <Slider
                        type="multiple"
                        bind:value={$form.experienceRange}
                        min={0}
                        max={15}
                        step={1}
                        class="w-full" />
                      <div class="text-muted-foreground flex justify-between text-xs">
                        <span>0 years</span>
                        <span>15+ years</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Auto-Apply Toggle -->
                <div
                  class="bg-muted/50 mt-6 flex items-center justify-between rounded-lg border p-4">
                  <div>
                    <Label for="auto-apply" class="text-sm font-medium"
                      >Enable Automatic Applications</Label>
                    <p class="text-muted-foreground text-xs">
                      Automatically apply to jobs that match your criteria
                    </p>
                  </div>
                  <Switch
                    id="auto-apply"
                    checked={Boolean($form.autoApplyEnabled)}
                    onCheckedChange={(checked) => {
                      $form.autoApplyEnabled = checked;
                    }} />
                </div>
              </div>
            {/if}
          </div>

          <Dialog.Footer class="border-border grid grid-cols-3 gap-4 border-t p-2">
            <Button variant="outline" onclick={() => (createDialogOpen = false)}>Cancel</Button>
            <Button type="submit" variant="default" disabled={!isFormValid() || $submitting}>
              {#if $submitting}
                Creating...
              {:else}
                Start Automation
              {/if}
            </Button>
          </Dialog.Footer>
        </form>
      </ScrollArea>
    </FeatureGuard>
  </Dialog.Content>
</Dialog.Root>
